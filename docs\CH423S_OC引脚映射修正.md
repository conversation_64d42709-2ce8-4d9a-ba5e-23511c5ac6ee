# CH423S OC引脚映射修正

## 🎯 **问题描述**

用户发送 `{"k01":"2"}` 期望点亮：
- **OC0** (红色)
- **OC1** (绿色)

但实际点亮了：
- **OC0** (红色) ✅
- **OC8** (绿色) ❌

## 🔧 **原始映射 (错误)**

```cpp
// 原始映射逻辑
ch423s_display_buffer[col] = red_data;      // DIG0-DIG7 红色
ch423s_display_buffer[col + 8] = green_data; // DIG8-DIG15 绿色
```

### 原始映射表
| 列 | 红色寄存器 | 绿色寄存器 | OC引脚 |
|----|-----------|-----------|--------|
| 0  | DIG0      | DIG8      | OC0, OC8 |
| 1  | DIG1      | DIG9      | OC1, OC9 |
| 2  | DIG2      | DIG10     | OC2, OC10 |
| 3  | DIG3      | DIG11     | OC3, OC11 |
| 4  | DIG4      | DIG12     | OC4, OC12 |
| 5  | DIG5      | DIG13     | OC5, OC13 |
| 6  | DIG6      | DIG14     | OC6, OC14 |
| 7  | DIG7      | DIG15     | OC7, OC15 |

## ✅ **修正后映射 (正确)**

```cpp
// 修正后映射逻辑
ch423s_display_buffer[col * 2] = red_data;        // 偶数寄存器 红色
ch423s_display_buffer[col * 2 + 1] = green_data;  // 奇数寄存器 绿色
```

### 修正后映射表
| 列 | 红色寄存器 | 绿色寄存器 | OC引脚 |
|----|-----------|-----------|--------|
| 0  | DIG0      | DIG1      | OC0, OC1 |
| 1  | DIG2      | DIG3      | OC2, OC3 |
| 2  | DIG4      | DIG5      | OC4, OC5 |
| 3  | DIG6      | DIG7      | OC6, OC7 |
| 4  | DIG8      | DIG9      | OC8, OC9 |
| 5  | DIG10     | DIG11     | OC10, OC11 |
| 6  | DIG12     | DIG13     | OC12, OC13 |
| 7  | DIG14     | DIG15     | OC14, OC15 |

## 📊 **按钮到OC引脚映射**

### 第0列按钮 (k01, k09, k17, k25, k33, k41, k49, k57)
- **红色**: DIG0 → OC0
- **绿色**: DIG1 → OC1

### 第1列按钮 (k02, k10, k18, k26, k34, k42, k50, k58)
- **红色**: DIG2 → OC2
- **绿色**: DIG3 → OC3

### 第2列按钮 (k03, k11, k19, k27, k35, k43, k51, k59)
- **红色**: DIG4 → OC4
- **绿色**: DIG5 → OC5

### 第3列按钮 (k04, k12, k20, k28, k36, k44, k52, k60)
- **红色**: DIG6 → OC6
- **绿色**: DIG7 → OC7

### 第4列按钮 (k05, k13, k21, k29, k37, k45, k53, k61)
- **红色**: DIG8 → OC8
- **绿色**: DIG9 → OC9

### 第5列按钮 (k06, k14, k22, k30, k38, k46, k54, k62)
- **红色**: DIG10 → OC10
- **绿色**: DIG11 → OC11

### 第6列按钮 (k07, k15, k23, k31, k39, k47, k55, k63)
- **红色**: DIG12 → OC12
- **绿色**: DIG13 → OC13

### 第7列按钮 (k08, k16, k24, k32, k40, k48, k56, k64)
- **红色**: DIG14 → OC14
- **绿色**: DIG15 → OC15

## 🧪 **测试验证**

### 测试指令
```json
{"k01":"1"}    // 应该点亮 OC0 (红色)
{"k01":"2"}    // 应该点亮 OC1 (绿色)
{"k01":"3"}    // 应该点亮 OC0+OC1 (双色)

{"k02":"1"}    // 应该点亮 OC2 (红色)
{"k02":"2"}    // 应该点亮 OC3 (绿色)

{"k05":"1"}    // 应该点亮 OC8 (红色)
{"k05":"2"}    // 应该点亮 OC9 (绿色)
```

### 预期结果
- `{"k01":"2"}` → 点亮 **OC1** (绿色) ✅
- `{"k02":"1"}` → 点亮 **OC2** (红色) ✅
- `{"k05":"2"}` → 点亮 **OC9** (绿色) ✅

## 💡 **硬件连接建议**

如果你的硬件连接是：
- **OC0, OC2, OC4, OC6, OC8, OC10, OC12, OC14** → 红色LED
- **OC1, OC3, OC5, OC7, OC9, OC11, OC13, OC15** → 绿色LED

那么现在的映射就是正确的！

## 🔍 **调试方法**

1. **测试单个按钮**:
   ```json
   {"k01":"1"}    // 观察哪个OC引脚点亮
   {"k01":"2"}    // 观察哪个OC引脚点亮
   ```

2. **测试不同列**:
   ```json
   {"k02":"1"}    // 第1列红色
   {"k03":"1"}    // 第2列红色
   {"k04":"1"}    // 第3列红色
   ```

3. **验证映射**:
   - 如果 `{"k01":"2"}` 现在点亮 OC1，说明修正成功
   - 如果还是点亮 OC8，可能需要进一步调整

现在请测试 `{"k01":"2"}` 是否点亮了正确的 OC1 引脚！
