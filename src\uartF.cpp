/**
 * @file uartF.cpp
 * @brief 串口通信模块实现
 * @details 处理串口通信、命令解析、JSON数据发送
 */

#include "uartF.h"
#include "logF.h"
#include "hubF.h"  // 添加hubF头文件

#define TAG "UART"

// 串口接收缓冲区
static char uart_buffer[UART_BUFFER_SIZE];
static uint8_t uart_index = 0;
static uint32_t last_receive_time = 0;  // 最后接收时间

// 组合按键管理
static PressedKey_t pressed_keys[MAX_PRESSED_KEYS];
static uint8_t pressed_count = 0;

// 模块状态
static bool uart_initialized = false;

/**
 * @brief 发送按键组合JSON数据
 * @details 发送格式规则:
 *          - 无按键: 不发送任何数据
 *          - 单按键: {"key":[按键ID,0]} - 用0占位
 *          - 多按键: {"key":[按键1,按键2,...]} - 保持原样
 */
static void sendKeyCombo() {
    // 无按键时不发送任何数据
    if (pressed_count == 0) {
        LOG_D(TAG, "无按键按下，不发送数据");
        return;  // 直接返回，不发送
    }

    Serial.print("{\"key\":[");

    if (pressed_count == 1) {
        // 单按键：添加0占位 -> [按键ID,0]
        Serial.printf("%d,0", pressed_keys[0].key_id);
        LOG_D(TAG, "发送单按键格式: [%d,0]", pressed_keys[0].key_id);
    } else {
        // 多按键：保持原样输出 -> [按键1,按键2,...]
        for (int i = 0; i < pressed_count; i++) {
            Serial.printf("%d", pressed_keys[i].key_id);
            if (i < pressed_count - 1) {
                Serial.print(",");
            }
        }
        LOG_D(TAG, "发送多按键格式，按键数量: %d", pressed_count);
    }

    Serial.print("]}");
}

/**
 * @brief 检查是否为JSON指令
 */
static bool isJsonCommand(const char* cmd) {
    if (!cmd) return false;

    // 简单检查：以{开头，以}结尾
    int len = strlen(cmd);
    return (len > 2 && cmd[0] == '{' && cmd[len-1] == '}');
}

/**
 * @brief 串口命令处理函数
 */
static void processSerialCommand(const char* command) {
    // 移除换行符
    char cmd[UART_BUFFER_SIZE];
    strncpy(cmd, command, sizeof(cmd) - 1);
    cmd[sizeof(cmd) - 1] = '\0';

    // 移除尾部的换行符和回车符
    int len = strlen(cmd);
    while (len > 0 && (cmd[len-1] == '\n' || cmd[len-1] == '\r')) {
        cmd[--len] = '\0';
    }

    // 忽略串口助手的自动发送信息
    if (strstr(cmd, "Welcome to UartAssist") != NULL) {
        return;  // 直接忽略，不处理
    }

    // 忽略空命令
    if (len == 0) {
        return;
    }

    // 检查是否为JSON指令
    if (isJsonCommand(cmd)) {
        LOG_I(TAG, "检测到JSON指令，转发给HUB: %s", cmd);
        hubProcessCommand(cmd);  // 转发给hubF处理
        return;
    }

    // 命令处理
    if (strcmp(cmd, "STATUS") == 0) {
        // 查询系统状态
        uint32_t uptime = millis();
        Serial.printf("RESPONSE:STATUS,PRESSED=%d,UPTIME=%lu,INITIALIZED=%d\n",
                     pressed_count, uptime, uart_initialized);

    } else if (strcmp(cmd, "HELP") == 0) {
        // 显示帮助信息
        Serial.println("RESPONSE:HELP");
        Serial.println("Available commands:");
        Serial.println("  STATUS    - Get system status");
        Serial.println("  HELP      - Show this help");
        Serial.println("  VERSION   - Show version info");
        Serial.println("  RESET     - Reset keyboard module");
        Serial.println("  SCAN      - Get current key states");
        Serial.println("  TEST      - Run self test");
        Serial.println("  DEBUG_ON  - Enable debug mode");
        Serial.println("  DEBUG_OFF - Disable debug mode");

    } else if (strcmp(cmd, "VERSION") == 0) {
        // 版本信息
        Serial.println("RESPONSE:VERSION");
        Serial.println("STM32 Keyboard Scanner v1.0");
        Serial.println("Matrix: 8x9, Build: " __DATE__ " " __TIME__);

    } else if (strcmp(cmd, "RESET") == 0) {
        // 重置键盘模块
        Serial.println("RESPONSE:RESET,OK");

    } else if (strcmp(cmd, "SCAN") == 0) {
        // 扫描当前按键状态
        Serial.println("RESPONSE:SCAN");
        for (int i = 0; i < pressed_count; i++) {
            Serial.printf("PRESSED:K%d\n", pressed_keys[i].key_id);
        }
        Serial.printf("SCAN_COMPLETE:TOTAL=%d\n", pressed_count);

    } else if (strcmp(cmd, "TEST") == 0) {
        // 自检测试
        Serial.println("RESPONSE:TEST");
        Serial.println("Self test started...");
        Serial.printf("Pressed keys: %d\n", pressed_count);
        Serial.printf("Initialized: %s\n", uart_initialized ? "YES" : "NO");
        Serial.printf("Uptime: %lu ms\n", millis());
        Serial.println("Self test completed");

    } else if (strcmp(cmd, "DEBUG_ON") == 0) {
        // 开启调试模式
        Serial.println("RESPONSE:DEBUG_ON,OK");

    } else if (strcmp(cmd, "DEBUG_OFF") == 0) {
        // 关闭调试模式
        Serial.println("RESPONSE:DEBUG_OFF,OK");

    } else {
        // 未知命令
        Serial.printf("RESPONSE:ERROR,UNKNOWN_COMMAND='%s'\n", cmd);
        Serial.println("Type 'HELP' for available commands");
    }
}

// ==================== 公共API函数 ====================

/**
 * @brief 初始化串口模块
 */
bool uartInit(void) {
    if (uart_initialized) {
        LOG_W(TAG, "串口模块已经初始化");
        return true;
    }

    LOG_I(TAG, "开始初始化串口模块");

    // 明确设置串口引脚
    Serial.setTx(PA9);
    Serial.setRx(PA10);
    LOG_I(TAG, "串口引脚设置: TX=PA9, RX=PA10");

    // 初始化串口通信
    Serial.begin(UART_BAUD_RATE);
    LOG_I(TAG, "串口波特率设置: %d", UART_BAUD_RATE);

    delay(500);  // 等待串口稳定
    LOG_I(TAG, "串口稳定延时完成");

    // 清空缓冲区和串口缓冲区
    pressed_count = 0;

    // 清空串口接收缓冲区中的残留数据
    while (Serial.available()) {
        Serial.read();
    }

    // 清空接收缓冲区
    uart_index = 0;
    memset(uart_buffer, 0, sizeof(uart_buffer));
    last_receive_time = 0;

    // 标记为已初始化
    uart_initialized = true;

    LOG_I(TAG, "串口模块初始化完成 - 波特率: %d (PA9:TX, PA10:RX)", UART_BAUD_RATE);
    LOG_I(TAG, "串口测试信息已发送，请检查上位机是否收到");
    return true;
}

/**
 * @brief 清空接收缓冲区
 */
static void clearReceiveBuffer(void) {
    uart_index = 0;
    memset(uart_buffer, 0, sizeof(uart_buffer));
    last_receive_time = 0;
    LOG_D(TAG, "缓冲区已清空");
}

/**
 * @brief 串口接收处理任务
 */
void uartReceiveTask(void) {
    if (!uart_initialized) {
        return;
    }

    uint32_t current_time = millis();

    // 检查缓冲区超时 (20ms无新数据则处理或清空)
    if (uart_index > 0 && last_receive_time > 0 &&
        (current_time - last_receive_time) > 20) {

        uart_buffer[uart_index] = '\0';

        // 检查是否为完整的JSON指令
        if (isJsonCommand(uart_buffer)) {
            LOG_I(TAG, "超时检测到完整JSON指令: '%s'", uart_buffer);
            processSerialCommand(uart_buffer);
        } else {
            LOG_W(TAG, "缓冲区超时，自动清空: '%.*s'", uart_index, uart_buffer);
        }

        clearReceiveBuffer();
    }

    // 调试：定期输出串口状态 (仅RTT)
    static uint32_t last_debug_time = 0;
    if (current_time - last_debug_time > 10000) {  // 每10秒输出一次
        LOG_D(TAG, "串口状态检查: 初始化=%s, 可用字节=%d",
              uart_initialized ? "是" : "否", Serial.available());
        last_debug_time = current_time;
    }

    while (Serial.available()) {
        char c = Serial.read();
        current_time = millis();  // 更新当前时间

        if (c == '\n' || c == '\r') {
            // 收到换行符，处理命令
            if (uart_index > 0) {
                uart_buffer[uart_index] = '\0';

                LOG_I(TAG, "接收到完整命令: '%s'", uart_buffer);

                processSerialCommand(uart_buffer);

                // 清空缓冲区
                clearReceiveBuffer();
            }
        } else if (c >= 32 && c <= 126) {  // 只接受可打印字符
            // 如果是新的接收序列，先清空旧数据
            if (uart_index == 0) {
                memset(uart_buffer, 0, sizeof(uart_buffer));
                LOG_D(TAG, "开始接收新指令...");
            }

            if (uart_index < UART_BUFFER_SIZE - 1) {
                // 添加字符到缓冲区
                uart_buffer[uart_index++] = c;
                last_receive_time = current_time;  // 更新接收时间

                // 检查是否收到完整的JSON指令
                if (c == '}' && uart_buffer[0] == '{') {
                    uart_buffer[uart_index] = '\0';
                    if (isJsonCommand(uart_buffer)) {
                        LOG_I(TAG, "检测到完整JSON指令: '%s'", uart_buffer);
                        processSerialCommand(uart_buffer);
                        clearReceiveBuffer();
                        return;  // 立即返回，不继续处理
                    }
                }

                // 只在特定情况下输出调试信息
                if (uart_index == 1 || c == '}') {
                    LOG_D(TAG, "当前缓冲区: '%.*s' (长度:%d)", uart_index, uart_buffer, uart_index);
                }
            } else {
                // 缓冲区满，重置
                LOG_W(TAG, "串口缓冲区溢出，重置");
                clearReceiveBuffer();
            }
        }
        // 移除忽略字符的调试输出
    }
}

/**
 * @brief 发送按键组合JSON数据 (外部调用接口)
 * @param keys 按键数组
 * @param count 按键数量
 * @details 发送格式规则:
 *          - 无按键: 不发送任何数据
 *          - 单按键: {"key":[按键ID,0]} - 用0占位
 *          - 多按键: {"key":[按键1,按键2,...]} - 保持原样
 */
void uartSendKeyCombo(const PressedKey_t* keys, uint8_t count) {
    // 无按键时不发送任何数据
    if (count == 0) {
        LOG_D(TAG, "外部调用：无按键按下，不发送数据");
        return;  // 直接返回，不发送
    }

    Serial.print("{\"key\":[");

    if (count == 1) {
        // 单按键：添加0占位 -> [按键ID,0]
        Serial.printf("%d,0", keys[0].key_id);
        LOG_D(TAG, "外部调用：发送单按键格式: [%d,0]", keys[0].key_id);
    } else {
        // 多按键：保持原样输出 -> [按键1,按键2,...]
        for (int i = 0; i < count; i++) {
            Serial.printf("%d", keys[i].key_id);
            if (i < count - 1) {
                Serial.print(",");
            }
        }
        LOG_D(TAG, "外部调用：发送多按键格式，按键数量: %d", count);
    }

    Serial.print("]}");
}

/**
 * @brief 添加按键到组合列表
 */
void uartAddPressedKey(uint8_t key_id) {
    // 检查是否已存在
    for (int i = 0; i < pressed_count; i++) {
        if (pressed_keys[i].key_id == key_id) {
            return;  // 已存在，不重复添加
        }
    }

    // 添加新按键（最多4个）
    if (pressed_count < MAX_PRESSED_KEYS) {
        pressed_keys[pressed_count].key_id = key_id;
        pressed_keys[pressed_count].press_time = millis();
        pressed_count++;

        // 按按下时间排序（先按下的在前）
        for (int i = pressed_count - 1; i > 0; i--) {
            if (pressed_keys[i].press_time < pressed_keys[i-1].press_time) {
                PressedKey_t temp = pressed_keys[i];
                pressed_keys[i] = pressed_keys[i-1];
                pressed_keys[i-1] = temp;
            } else {
                break;
            }
        }

        sendKeyCombo();
    }
}

/**
 * @brief 从组合列表移除按键 (后台处理，不发送串口消息)
 */
void uartRemovePressedKey(uint8_t key_id) {
    for (int i = 0; i < pressed_count; i++) {
        if (pressed_keys[i].key_id == key_id) {
            // 移除该按键，后面的前移
            for (int j = i; j < pressed_count - 1; j++) {
                pressed_keys[j] = pressed_keys[j + 1];
            }
            pressed_count--;
            // 不调用sendKeyCombo() - 只在后台更新状态，不发送串口消息
            break;
        }
    }
}

/**
 * @brief 获取当前按下的按键数量
 */
uint8_t uartGetPressedCount(void) {
    return pressed_count;
}

/**
 * @brief 获取串口模块初始化状态
 */
bool uartIsInitialized(void) {
    return uart_initialized;
}

/**
 * @brief 串口连接测试函数
 */
void uartTestConnection(void) {
    if (!uart_initialized) {
        LOG_W(TAG, "串口未初始化，无法测试");
        return;
    }

    LOG_I(TAG, "串口连接测试完成");
    LOG_I(TAG, "波特率: %d, 缓冲区大小: %d", UART_BAUD_RATE, UART_BUFFER_SIZE);
}

/**
 * @brief 串口模块任务
 */
void uartModuleTask(void) {
    static bool firstRun = true;
    static uint32_t last_test_time = 0;

    // 首次运行时初始化串口模块
    if (firstRun) {
        if (uartInit()) {
            LOG_I(TAG, "串口模块任务初始化成功");
            // 延时后发送测试信息
            delay(1000);
            uartTestConnection();
        } else {
            LOG_E(TAG, "串口模块任务初始化失败");
        }
        firstRun = false;
        return;
    }

    // 定期RTT调试信息 (每30秒)
    uint32_t current_time = millis();
    if (current_time - last_test_time > 30000) {
        LOG_D(TAG, "串口模块运行正常，运行时间: %lu ms", current_time);
        last_test_time = current_time;
    }

    // 处理串口接收
    uartReceiveTask();
}