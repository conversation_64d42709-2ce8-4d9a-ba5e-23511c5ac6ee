# CH423S 64位按钮控制系统

## 🎯 **设计理念**

实现了一个高效的64位按钮LED状态控制系统，支持：
- 64个按钮的独立LED控制
- 高速循环数据发送
- 简洁的API接口
- 灵活的状态管理

## 📊 **数据结构**

### 核心数组
```cpp
static uint8_t button_led_states[64];  // 64个按钮的LED状态
```

### 状态定义
- `0`: 关闭所有LED
- `1`: 点亮红色LED
- `2`: 点亮绿色LED  
- `3`: 点亮红绿双色LED

### 按钮索引映射
```cpp
// 按钮索引 = 行号 * 8 + 列号
// 例如：按钮[2,3] = 2 * 8 + 3 = 19
uint8_t button_index = row * 8 + col;
```

## 🚀 **API接口**

### 基本控制
```cpp
// 设置单个按钮状态 (直接索引)
ch423sSetButtonState(0, 1);     // 按钮0设为红色
ch423sSetButtonState(63, 2);    // 按钮63设为绿色

// 设置单个按钮状态 (行列方式)
ch423sSetButtonLed(0, 0, 1);    // 第0行第0列设为红色
ch423sSetButtonLed(7, 7, 3);    // 第7行第7列设为双色

// 获取按钮状态
uint8_t state = ch423sGetButtonState(10);

// 清除所有LED
ch423sClearAllLeds();
```

### 批量控制
```cpp
// 批量设置所有按钮状态
uint8_t pattern[64] = {1,2,3,0, ...};  // 自定义模式
ch423sSetAllButtonStates(pattern);

// 获取所有按钮状态
uint8_t current_states[64];
ch423sGetAllButtonStates(current_states);
```

## ⚡ **高速循环机制**

### 工作原理
1. **缓冲区更新**: 将64位按钮状态转换为16个8位寄存器数据
2. **循环发送**: 每次任务调用只发送一个寄存器，实现高速循环
3. **无阻塞**: 不等待完整发送完成，保持系统响应性

### 时序控制
```cpp
// 每1ms更新一次
if (current_time - last_update_time >= 1) {
    updateDisplayBuffer();  // 更新缓冲区
    sendDisplayData();      // 发送一个寄存器
}
```

### 完整刷新周期
- 16个寄存器 × 1ms = 16ms完整刷新一次
- 刷新频率: ~62.5Hz
- 人眼感知: 无闪烁，流畅显示

## 🔄 **数据映射流程**

### 1. 按钮状态 → 显示缓冲区
```cpp
for (uint8_t col = 0; col < 8; col++) {
    uint8_t red_data = 0, green_data = 0;
    
    for (uint8_t row = 0; row < 8; row++) {
        uint8_t button_index = row * 8 + col;
        uint8_t state = button_led_states[button_index];
        
        if (state == 1 || state == 3) red_data |= (1 << row);
        if (state == 2 || state == 3) green_data |= (1 << row);
    }
    
    ch423s_display_buffer[col] = red_data;      // DIG0-DIG7
    ch423s_display_buffer[col + 8] = green_data; // DIG8-DIG15
}
```

### 2. 显示缓冲区 → CH423S寄存器
```cpp
// 循环发送，每次一个寄存器
uint16_t cmd = CH423S_DIG_BASE + (send_index << 8) + ch423s_display_buffer[send_index];
ch423sWrite(cmd);
```

## 🧪 **测试功能**

### 演示阶段
1. **阶段0**: 基本输出测试
2. **阶段1**: 逐列点亮测试
3. **阶段2**: 垂直累积扫描
4. **阶段3**: 64位按钮矩阵测试

### 64位按钮测试内容
```cpp
// 测试1: 逐个点亮 (0-63)
for (uint8_t i = 0; i < 64; i++) {
    ch423sSetButtonState(i, 1);
}

// 测试2: 棋盘模式
for (uint8_t i = 0; i < 64; i++) {
    uint8_t state = ((i/8 + i%8) % 2) ? 1 : 2;
    ch423sSetButtonState(i, state);
}

// 测试3: 边框模式
uint8_t border_pattern[64] = {0};
// 设置边框为红色...
ch423sSetAllButtonStates(border_pattern);
```

## 💡 **使用示例**

### 创建自定义模式
```cpp
// 创建一个笑脸模式
uint8_t smile_pattern[64] = {0};

// 眼睛 (红色)
smile_pattern[18] = 1;  // 左眼
smile_pattern[21] = 1;  // 右眼

// 嘴巴 (绿色)
smile_pattern[42] = 2;
smile_pattern[43] = 2;
smile_pattern[44] = 2;
smile_pattern[45] = 2;

ch423sSetAllButtonStates(smile_pattern);
```

### 动态效果
```cpp
// 流水灯效果
static uint8_t current_pos = 0;

ch423sClearAllLeds();
ch423sSetButtonState(current_pos, 1);
current_pos = (current_pos + 1) % 64;
```

## 🔧 **性能优势**

1. **高效更新**: 每次只发送8位数据，减少I2C传输时间
2. **无阻塞**: 不等待完整传输，保持系统响应性
3. **低延迟**: 1ms更新间隔，实时响应
4. **灵活控制**: 支持单个和批量操作
5. **内存优化**: 64字节状态数组，内存占用小

## 📝 **总结**

这个64位按钮控制系统完美解决了你提出的需求：
- ✅ 64位数据矩阵对应每个按钮
- ✅ 状态值控制LED颜色 (0/1/2/3)
- ✅ 高速循环发送，每次8位数据
- ✅ 修改数据矩阵即可实现测试效果
- ✅ 简洁高效的API接口

现在你可以通过简单的数组操作来控制整个8×8 LED矩阵！
