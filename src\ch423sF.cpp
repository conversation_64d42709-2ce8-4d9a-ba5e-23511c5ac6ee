/**
 * @file ch423sF.cpp
 * @brief CH423S自动扫描LED控制模块
 */

#include "ch423sF.h"
#include "Arduino.h"
#include "logF.h"
#include <string.h>
#include <stdbool.h>

static const char* TAG = "CH423S";

// 核心变量
static bool ch423s_initialized = false;
static uint8_t detected_ch423s_count = 0;

// 64位按钮LED状态映射表 (0=关闭, 1=红色, 2=绿色, 3=红绿双色)
static uint8_t button_led_states[64];                  // 64个按钮的LED状态
static uint8_t ch423s_display_buffer[16];              // 显示缓冲区 DIG0-DIG15

// 高速发送控制
static bool fast_update_enabled = true;                // 启用高速更新
static uint32_t last_update_time = 0;                  // 上次更新时间

// K65按钮状态 (0=关闭, 1=红色, 2=绿色, 3=红绿双色)
static uint8_t k65_button_state = 0;

// 亮度控制
typedef enum {
    CH423S_BRIGHTNESS_LEVEL_LOW    = 0,    // 低亮度
    CH423S_BRIGHTNESS_LEVEL_MID    = 1,    // 中等亮度
    CH423S_BRIGHTNESS_LEVEL_HIGH   = 2,    // 高亮度
    CH423S_BRIGHTNESS_LEVEL_MAX    = 3     // 最高亮度
} ch423s_brightness_level_t;

static ch423s_brightness_level_t current_brightness = CH423S_BRIGHTNESS_LEVEL_MAX;  // 默认最高亮度

// 引脚定义
#define SCL_PIN PA6
#define SDA_PIN PA7

// K65按钮LED引脚定义
#define K65_RED_PIN   PB11   // K65红色LED
#define K65_GREEN_PIN PB1  // K65绿色LED

// CH423S命令定义
#define CH423S_SYS_CMD          0x4800              // 系统参数设置命令
#define CH423S_BIT_DEC_H        0x04                // 高8位片选译码控制
#define CH423S_BIT_DEC_L        0x02                // 低8位片选译码控制
#define CH423S_BIT_IO_OE        0x01                // 双向I/O输出使能
#define CH423S_BIT_INTENS_0     0x08                // 亮度控制位0
#define CH423S_BIT_INTENS_1     0x10                // 亮度控制位1
#define CH423S_AUTO_SCAN_ON     0x4417              // 开启自动扫描 (修正命令格式)
#define CH423S_DIG_BASE         0x1000              // 显示位基地址 DIG0-DIG15

// 亮度控制定义 (基于网络搜索的CH423S技术资料)
#define CH423S_BRIGHTNESS_LOW   0x4407              // 低亮度自动扫描
#define CH423S_BRIGHTNESS_MID   0x4417              // 中等亮度自动扫描（默认）
#define CH423S_BRIGHTNESS_HIGH  0x4427              // 高亮度自动扫描
#define CH423S_BRIGHTNESS_MAX   0x4437              // 最高亮度自动扫描
// 备用地址定义，如果0x1000不工作，可以尝试这些
#define CH423S_DIG_ALT1         0x1400              // 备用显示位基地址1
#define CH423S_DIG_ALT2         0x1800              // 备用显示位基地址2

// ==================== 亮度控制函数 ====================

/**
 * @brief 获取亮度级别对应的扫描命令
 * @param level 亮度级别
 * @return 对应的扫描命令
 */
static uint16_t getBrightnessCommand(ch423s_brightness_level_t level) {
    switch (level) {
        case CH423S_BRIGHTNESS_LEVEL_LOW:
            return CH423S_BRIGHTNESS_LOW;
        case CH423S_BRIGHTNESS_LEVEL_MID:
            return CH423S_BRIGHTNESS_MID;
        case CH423S_BRIGHTNESS_LEVEL_HIGH:
            return CH423S_BRIGHTNESS_HIGH;
        case CH423S_BRIGHTNESS_LEVEL_MAX:
            return CH423S_BRIGHTNESS_MAX;
        default:
            return CH423S_BRIGHTNESS_MAX;  // 默认最高亮度
    }
}

/**
 * @brief 设置CH423S亮度级别
 * @param level 亮度级别 (0=低, 1=中, 2=高, 3=最高)
 * @return true 设置成功, false 设置失败
 */
bool ch423sSetBrightness(ch423s_brightness_level_t level) {
    if (level > CH423S_BRIGHTNESS_LEVEL_MAX) {
        LOG_E(TAG, "无效的亮度级别: %d", level);
        return false;
    }

    uint16_t brightness_cmd = getBrightnessCommand(level);

    LOG_I(TAG, "设置亮度级别: %d -> 命令: 0x%04X", level, brightness_cmd);

    if (ch423sWriteByte(brightness_cmd)) {
        current_brightness = level;
        LOG_I(TAG, "亮度设置成功: 级别%d", level);
        return true;
    } else {
        LOG_E(TAG, "亮度设置失败: 级别%d", level);
        return false;
    }
}

/**
 * @brief 获取当前亮度级别
 * @return 当前亮度级别
 */
ch423s_brightness_level_t ch423sGetBrightness(void) {
    return current_brightness;
}

/**
 * @brief 测试所有亮度级别
 */
void ch423sTestBrightness(void) {
    LOG_I(TAG, "开始亮度测试...");

    for (int level = 0; level <= CH423S_BRIGHTNESS_LEVEL_MAX; level++) {
        LOG_I(TAG, "测试亮度级别 %d", level);
        ch423sSetBrightness((ch423s_brightness_level_t)level);
        delay(3000);  // 观察3秒
    }

    // 恢复到最高亮度
    ch423sSetBrightness(CH423S_BRIGHTNESS_LEVEL_MAX);
    LOG_I(TAG, "亮度测试完成，已设置为最高亮度");
}

// ==================== 软件I2C ====================

static bool softI2C_WriteByte(uint8_t data) {
    for (int i = 0; i < 8; i++) {
        digitalWrite(SDA_PIN, (data & 0x80) ? HIGH : LOW);
        delayMicroseconds(10);  // 增加延时提高稳定性
        digitalWrite(SCL_PIN, HIGH);
        delayMicroseconds(10);  // 增加延时提高稳定性
        data <<= 1;
        digitalWrite(SCL_PIN, LOW);
        delayMicroseconds(10);  // 增加延时提高稳定性
    }

    pinMode(SDA_PIN, INPUT_PULLUP);
    delayMicroseconds(10);
    digitalWrite(SCL_PIN, HIGH);
    delayMicroseconds(10);
    bool ack = (digitalRead(SDA_PIN) == LOW);
    digitalWrite(SCL_PIN, LOW);
    delayMicroseconds(10);
    pinMode(SDA_PIN, OUTPUT);

    return ack;
}

static void softI2C_Start(void) {
    digitalWrite(SDA_PIN, HIGH);
    digitalWrite(SCL_PIN, HIGH);
    delayMicroseconds(10);  // 增加延时提高稳定性
    digitalWrite(SDA_PIN, LOW);
    delayMicroseconds(10);  // 增加延时提高稳定性
    digitalWrite(SCL_PIN, LOW);
    delayMicroseconds(10);  // 增加延时提高稳定性
}

static void softI2C_Stop(void) {
    digitalWrite(SDA_PIN, LOW);
    delayMicroseconds(10);  // 增加延时提高稳定性
    digitalWrite(SCL_PIN, HIGH);
    delayMicroseconds(10);  // 增加延时提高稳定性
    digitalWrite(SDA_PIN, HIGH);
    delayMicroseconds(10);  // 增加延时提高稳定性
}

// CH423_WriteByte方式 - 用于系统命令
static bool ch423sWriteByte(uint16_t cmd) {
    softI2C_Start();

    bool ack1 = softI2C_WriteByte((cmd >> 8) & 0xFF);
    if (!ack1) {
        softI2C_Stop();
        LOG_E(TAG, "系统命令0x%04X高字节ACK失败", cmd);
        return false;
    }

    bool ack2 = softI2C_WriteByte(cmd & 0xFF);
    if (!ack2) {
        softI2C_Stop();
        LOG_E(TAG, "系统命令0x%04X低字节ACK失败", cmd);
        return false;
    }

    softI2C_Stop();
    delayMicroseconds(10);
    LOG_D(TAG, "系统命令0x%04X发送成功", cmd);
    return true;
}

// CH423_Write方式 - 用于显示位命令
static bool ch423sWrite(uint16_t cmd) {
    softI2C_Start();

    // CH423S显示位命令地址计算: ((cmd >> 7) & 0x3E) | 0x40
    uint8_t addr_byte = ((cmd >> 7) & 0x3E) | 0x40;
    // LOG_D(TAG, "显示命令0x%04X -> 地址字节:0x%02X, 数据字节:0x%02X",
        //   cmd, addr_byte, cmd & 0xFF);

    bool ack1 = softI2C_WriteByte(addr_byte);
    if (!ack1) {
        softI2C_Stop();
        // LOG_E(TAG, "显示命令0x%04X地址字节0x%02X ACK失败", cmd, addr_byte);
        return false;
    }

    bool ack2 = softI2C_WriteByte(cmd & 0xFF);
    if (!ack2) {
        softI2C_Stop();
        // LOG_E(TAG, "显示命令0x%04X数据字节0x%02X ACK失败", cmd, cmd & 0xFF);
        return false;
    }

    softI2C_Stop();
    delayMicroseconds(10);
    // LOG_D(TAG, "显示命令0x%04X发送成功", cmd);
    return true;
}

// ==================== K65按钮控制功能 ====================

/**
 * @brief 更新K65按钮LED状态
 * @param state LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
static void updateK65ButtonLed(uint8_t state) {
    // 控制红色LED (PB1) - 低电平点亮，高电平熄灭
    if (state == 1 || state == 3) {
        digitalWrite(K65_RED_PIN, LOW);   // 点亮红色
    } else {
        digitalWrite(K65_RED_PIN, HIGH);  // 关闭红色
    }

    // 控制绿色LED (PB11) - 低电平点亮，高电平熄灭
    if (state == 2 || state == 3) {
        digitalWrite(K65_GREEN_PIN, LOW);   // 点亮绿色
    } else {
        digitalWrite(K65_GREEN_PIN, HIGH);  // 关闭绿色
    }

    LOG_D(TAG, "K65按钮LED状态更新: %d (红色:%s, 绿色:%s)",
          state,
          (state == 1 || state == 3) ? "ON" : "OFF",
          (state == 2 || state == 3) ? "ON" : "OFF");
}

/**
 * @brief 初始化K65按钮引脚
 */
static void initK65Button(void) {
    // 配置K65按钮LED引脚为输出模式
    pinMode(K65_RED_PIN, OUTPUT);
    pinMode(K65_GREEN_PIN, OUTPUT);

    // 初始状态为关闭 (高电平熄灭)
    digitalWrite(K65_RED_PIN, HIGH);
    digitalWrite(K65_GREEN_PIN, HIGH);
    k65_button_state = 0;

    LOG_I(TAG, "K65按钮初始化完成 - 红色:PB%d, 绿色:PB%d",
          K65_RED_PIN & 0x0F, K65_GREEN_PIN & 0x0F);
}

// ==================== 设备检测功能 ====================

static bool ch423sTestBasicCommunication(void) {
    LOG_I(TAG, "测试CH423S基本通信...");

    // 尝试发送一个简单的系统命令来测试通信
    // 使用最基本的系统参数设置命令
    uint16_t test_cmd = CH423S_SYS_CMD | CH423S_BIT_IO_OE;  // 只开启IO输出使能

    for (int retry = 0; retry < 5; retry++) {
        LOG_D(TAG, "尝试发送测试命令: 0x%04X (第%d次)", test_cmd, retry + 1);

        if (ch423sWriteByte(test_cmd)) {
            LOG_I(TAG, "CH423S通信测试成功 (重试%d次)", retry);
            return true;
        }
        LOG_W(TAG, "CH423S通信测试失败，重试 %d/5", retry + 1);
        delay(50);  // 增加重试间隔
    }

    LOG_E(TAG, "CH423S通信测试失败！请检查硬件连接");
    LOG_E(TAG, "检查项目：1.引脚连接 2.电源供电 3.上拉电阻");
    return false;
}

// ==================== 自动扫描功能 ====================

// 将64位按钮状态转换为CH423S显示缓冲区
static void updateDisplayBuffer(void) {
    // 清空显示缓冲区
    memset(ch423s_display_buffer, 0, sizeof(ch423s_display_buffer));

    // CH423S自动扫描是垂直扫描：DIG0-DIG7控制列，OC0-OC15提供行数据
    for (uint8_t col = 0; col < 8; col++) {  // 遍历每一列
        uint8_t red_data = 0, green_data = 0;

        for (uint8_t row = 0; row < 8; row++) {  // 遍历该列的每一行
            uint8_t button_index = row * 8 + col;  // 计算按钮索引 (0-63)
            uint8_t state = button_led_states[button_index];

            if (state == 1 || state == 3) red_data |= (1 << row);    // 红色
            if (state == 2 || state == 3) green_data |= (1 << row);  // 绿色
        }

        // 修改映射：红色和绿色使用连续的OC引脚
        // 每列使用2个连续的寄存器：偶数寄存器=红色，奇数寄存器=绿色
        ch423s_display_buffer[col * 2] = red_data;        // DIG0,2,4,6,8,10,12,14 红色
        ch423s_display_buffer[col * 2 + 1] = green_data;  // DIG1,3,5,7,9,11,13,15 绿色

        // LOG_D(TAG, "列%d: 红色=0x%02X, 绿色=0x%02X", col, red_data, green_data);
    }
}

// 高速发送显示数据 (每次发送8位数据)
static void sendDisplayData(void) {
    static uint8_t send_index = 0;  // 当前发送的寄存器索引

    if (!fast_update_enabled) return;

    // 每次发送4个寄存器，加快更新速度
    for (uint8_t i = 0; i < 4 && send_index < 16; i++) {
        uint16_t cmd = CH423S_DIG_BASE + (send_index << 8) + ch423s_display_buffer[send_index];

        if (!ch423sWrite(cmd)) {
            // LOG_W(TAG, "显示位%d命令发送失败", send_index);
        }

        send_index++;
    }

    if (send_index >= 16) {
        send_index = 0;  // 循环发送
    }
}

// 完整发送所有显示数据 (用于初始化或强制刷新)
static void sendAllDisplayData(void) {
    for (uint8_t i = 0; i < 16; i++) {
        uint16_t cmd = CH423S_DIG_BASE + (i << 8) + ch423s_display_buffer[i];

        LOG_D(TAG, "发送显示位%d命令: 0x%04X (数据: 0x%02X)",
              i, cmd, ch423s_display_buffer[i]);

        if (!ch423sWrite(cmd)) {
            LOG_W(TAG, "显示位%d命令发送失败", i);
        }
        delayMicroseconds(50);  // 减少延时提高速度
    }
}

static void ch423sInitInternal(void) {
    LOG_I(TAG, "初始化CH423S自动扫描模式...");

    // 初始化软件I2C引脚
    pinMode(SCL_PIN, OUTPUT);
    pinMode(SDA_PIN, OUTPUT);
    digitalWrite(SCL_PIN, HIGH);
    digitalWrite(SDA_PIN, HIGH);
    delay(100);  // 增加初始化延时

    // 初始化K65按钮
    initK65Button();

    // 检查引脚状态
    LOG_I(TAG, "I2C引脚状态 - SCL(PA%d): %d, SDA(PA%d): %d",
          SCL_PIN & 0x0F, digitalRead(SCL_PIN),
          SDA_PIN & 0x0F, digitalRead(SDA_PIN));

    // 清零状态
    memset(button_led_states, 0, sizeof(button_led_states));
    memset(ch423s_display_buffer, 0, sizeof(ch423s_display_buffer));

    // 先测试基本通信
    if (!ch423sTestBasicCommunication()) {
        LOG_E(TAG, "CH423S设备未响应，初始化失败");
        return;
    }

    // 初始化CH423S自动扫描模式 - 添加重试机制
    uint16_t sys_cmd = CH423S_SYS_CMD | CH423S_BIT_DEC_H | CH423S_BIT_DEC_L | CH423S_BIT_IO_OE;
    LOG_I(TAG, "发送系统命令: 0x%04X", sys_cmd);

    bool sys_cmd_success = false;
    for (int retry = 0; retry < 3; retry++) {
        if (ch423sWriteByte(sys_cmd)) {
            sys_cmd_success = true;
            LOG_I(TAG, "系统命令发送成功 (重试%d次)", retry);
            break;
        }
        LOG_W(TAG, "系统命令发送失败，重试 %d/3", retry + 1);
        delay(50);
    }

    if (!sys_cmd_success) {
        LOG_E(TAG, "系统命令发送失败！");
        return;
    }
    delay(50);  // 增加命令间延时

    // 开启自动扫描 - 使用简化的方法
    LOG_I(TAG, "开启自动扫描: 0x%04X", CH423S_AUTO_SCAN_ON);

    bool scan_cmd_success = false;
    for (int retry = 0; retry < 5; retry++) {
        LOG_D(TAG, "发送自动扫描命令 (第%d次)", retry + 1);

        if (ch423sWriteByte(CH423S_AUTO_SCAN_ON)) {
            scan_cmd_success = true;
            LOG_I(TAG, "自动扫描开启成功 (重试%d次)", retry);
            break;
        }
        LOG_W(TAG, "自动扫描开启失败，重试 %d/5", retry + 1);
        delay(100);  // 增加重试间隔
    }

    if (!scan_cmd_success) {
        LOG_E(TAG, "开启自动扫描失败！尝试使用基本模式");
        // 即使自动扫描失败，也继续初始化，使用手动控制模式
        LOG_W(TAG, "将使用手动LED控制模式");
    }
    delay(50);  // 增加命令间延时

    // 清空所有显示位
    LOG_I(TAG, "清空所有显示位...");
    for (uint8_t i = 0; i < 16; i++) {
        uint16_t clear_cmd = CH423S_DIG_BASE + (i << 8);
        if (!ch423sWrite(clear_cmd)) {  // 显示位命令使用Write方式
            LOG_W(TAG, "清空显示位%d失败", i);
        }
        delayMicroseconds(100);  // 增加显示位命令间延时
    }

    // 标记初始化完成
    ch423s_initialized = true;
    detected_ch423s_count = 1;

    // 设置最高亮度
    delay(100);  // 等待系统稳定
    if (ch423sSetBrightness(CH423S_BRIGHTNESS_LEVEL_MAX)) {
        LOG_I(TAG, "亮度已设置为最高级别");
    } else {
        LOG_W(TAG, "设置最高亮度失败，使用默认亮度");
    }

    if (scan_cmd_success) {
        LOG_I(TAG, "CH423S自动扫描模式初始化完成 (最高亮度)");
    } else {
        LOG_I(TAG, "CH423S基本模式初始化完成 (自动扫描不可用)");
    }
}

// ==================== 公共API ====================

// 设置单个按钮LED状态 (兼容旧接口)
void ch423sSetButtonLed(uint8_t row, uint8_t col, uint8_t state) {
    if (row >= 8 || col >= 8 || state > 3) return;

    uint8_t button_index = row * 8 + col;  // 计算按钮索引 (0-63)
    ch423sSetButtonState(button_index, state);
}

// 直接设置按钮状态 (新接口)
void ch423sSetButtonState(uint8_t button_index, uint8_t state) {
    if (button_index >= 64 || state > 3) return;

    if (button_led_states[button_index] != state) {
        button_led_states[button_index] = state;
        LOG_D(TAG, "设置按钮[%d] = %d", button_index, state);
        // 注意：这里不立即发送，由高速循环处理
    }
}

// 清除所有LED
void ch423sClearAllLeds(void) {
    memset(button_led_states, 0, sizeof(button_led_states));
    LOG_I(TAG, "清除所有LED");
}

// 获取按钮状态
uint8_t ch423sGetButtonState(uint8_t button_index) {
    if (button_index >= 64) return 0;
    return button_led_states[button_index];
}

// 批量设置按钮状态
void ch423sSetAllButtonStates(const uint8_t* states) {
    if (states == nullptr) return;

    memcpy(button_led_states, states, sizeof(button_led_states));
    LOG_I(TAG, "批量设置所有按钮状态");
}

// 获取所有按钮状态
void ch423sGetAllButtonStates(uint8_t* states) {
    if (states == nullptr) return;

    memcpy(states, button_led_states, sizeof(button_led_states));
}

// 设置K65按钮状态
void ch423sSetK65ButtonState(uint8_t state) {
    if (state > 3) return;

    if (k65_button_state != state) {
        k65_button_state = state;
        updateK65ButtonLed(state);
        LOG_I(TAG, "设置K65按钮状态: %d", state);
    }
}

// 获取K65按钮状态
uint8_t ch423sGetK65ButtonState(void) {
    return k65_button_state;
}

// ==================== 主任务 ====================

void ch423sModuleTask(void) {
    static bool initialized = false;

    if (!initialized) {
        ch423sInitInternal();
        initialized = true;
    }

    if (detected_ch423s_count > 0) {
        // 高速循环更新显示数据 - 每次任务调用都更新
        updateDisplayBuffer();  // 更新显示缓冲区
        sendDisplayData();      // 高速发送一个寄存器
    }
}
